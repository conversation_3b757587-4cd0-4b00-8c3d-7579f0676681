## Local Development Setup

### 1️⃣ Install PostgreSQL

**Mac (Homebrew):**
```bash
brew install postgresql
brew services start postgresql
```

**Windows:**
Download installer from: https://www.postgresql.org/download/windows/

### 2️⃣ Create a Database and Verify Roles

In terminal:
```bash
psql -U your_mac_username -h localhost -d postgres
\du
```
Use the role listed under `Role name` that has superuser privileges (like `nicolasgovea`).

Create your development database:
```sql
CREATE DATABASE software_catalog;
\q
```

### 3️⃣ Set Up Environment Variables

Create a `.env` file:
```
DATABASE_URL="postgresql://your_role@localhost:5432/software_catalog"
```
Replace `your_role` with the PostgreSQL role confirmed in Step 2. Add `:yourpassword` if the role requires it.

### 4️⃣ Apply Prisma Migrations

Run:
```bash
npx prisma migrate dev --name init
npx prisma generate
npx prisma studio
```

- `migrate dev` applies your schema.
- `generate` creates the Prisma client.
- `studio` opens a browser UI to manage database entries.

### 5️⃣ Start Development Server

Run:
```bash
yarn dev
```

Server should be running on `http://localhost:5000`.

### 6️⃣ Request Validation Using Zod

To validate incoming product data, Zod is used as middleware in `src/middlewares/validateProduct.ts`. This ensures requests contain valid fields before hitting your route handlers.

Example usage in your route file:

```ts
import { validateProduct } from '../middlewares/validateProduct';

router.post('/', validateProduct, createProduct);
```

Adjust and expand schemas as needed for different routes.

#### ✅ Validation Response Examples

**Valid Request Example:**

```bash
curl -X POST http://localhost:5050/api/products \
-H "Content-Type: application/json" \
-d '{
  "name": "Example Product",
  "description": "Sample description",
  "affiliateUrl": "https://example.com"
}'
```

**Response:**
A JSON object containing the newly created product.

**Invalid Request Example:**

```bash
curl -X POST http://localhost:5050/api/products \
-H "Content-Type: application/json" \
-d '{
  "name": "",
  "description": 123,
  "affiliateUrl": "invalid-url"
}'
```

**Response:**
```json
{
  "_errors": [],
  "name": {
    "_errors": [
      "String must contain at least 1 character(s)"
    ]
  },
  "description": {
    "_errors": [
      "Expected string, received number"
    ]
  },
  "affiliateUrl": {
    "_errors": [
      "Invalid url"
    ]
  }
}
```

To validate incoming product data, Zod is used as middleware in `src/middlewares/validateProduct.ts`. This ensures requests contain valid fields before hitting your route handlers.

Example usage in your route file:

```ts
import { validateProduct } from '../middlewares/validateProduct';

router.post('/', validateProduct, createProduct);
```

Adjust and expand schemas as needed for different routes.