import request from 'supertest';
import app from '../../server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

beforeAll(async () => {
  // clear the products table before tests
  await prisma.product.deleteMany();
});

afterAll(async () => {
  await prisma.$disconnect();
});

describe('Products API', () => {
  it('creates a product', async () => {
    const payload = {
      name: 'Test Product',
      description: 'Desc',
      category: 'Cat',
      affiliateUrl: 'https://example.com',
    };
    const res = await request(app)
      .post('/api/products')
      .send(payload);

    expect(res.status).toBe(200);
    expect(res.body).toMatchObject(payload);
    expect(res.body).toHaveProperty('id');
  });

  it('lists products', async () => {
    const res = await request(app).get('/api/products');
    expect(res.status).toBe(200);
    expect(Array.isArray(res.body)).toBe(true);
    expect(res.body.length).toBeGreaterThan(0);
  });
});
