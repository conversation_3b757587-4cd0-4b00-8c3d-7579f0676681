import { PrismaClient } from '@prisma/client';
import { Request, Response } from 'express';
const prisma = new PrismaClient();

export const getProducts = async (
  req: Request,
  res: Response,
): Promise<void> => {
  try {
    const products = await prisma.product.findMany();
    res.json(products);
  } catch (error: unknown) {
    // Narrow to built-in Error
    if (error instanceof Error) {
      res.status(500).json({ error: error.message });
    }
    // Fallback for truly unknown throws
    res.status(500).json({ error: 'An unexpected error occurred.' });
  }
};

export const createProduct = async (
  req: Request,
  res: Response,
): Promise<void> => {
  try {
    const { name, description, category, affiliateUrl } = req.body;
    const newProduct = await prisma.product.create({
      data: { name, description, category, affiliateUrl },
    });
    res.json(newProduct);
  } catch (error: unknown) {
    // Narrow to built-in Error
    if (error instanceof Error) {
      res.status(500).json({ error: error.message });
    }
    // Fallback for truly unknown throws
    res.status(500).json({ error: 'An unexpected error occurred.' });
  }
};

export const updateProduct = async (
  req: Request,
  res: Response,
): Promise<void> => {
  try {
    const { id } = req.params;
    const data = req.body;
    const updatedProduct = await prisma.product.update({
      where: { id: parseInt(id) },
      data,
    });
    res.json(updatedProduct);
  } catch (error: unknown) {
    // Narrow to built-in Error
    if (error instanceof Error) {
      res.status(500).json({ error: error.message });
    }
    // Fallback for truly unknown throws
    res.status(500).json({ error: 'An unexpected error occurred.' });
  }
};

export const deleteProduct = async (
  req: Request,
  res: Response,
): Promise<void> => {
  try {
    const { id } = req.params;
    await prisma.product.delete({ where: { id: parseInt(id) } });
    res.json({ message: 'Product deleted' });
  } catch (error: unknown) {
    // Narrow to built-in Error
    if (error instanceof Error) {
      res.status(500).json({ error: error.message });
    }
    // Fallback for truly unknown throws
    res.status(500).json({ error: 'An unexpected error occurred.' });
  }
};
