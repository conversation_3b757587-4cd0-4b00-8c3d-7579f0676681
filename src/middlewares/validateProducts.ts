import { z } from 'zod';
import { Request, Response, NextFunction } from 'express';

const productSchema = z.object({
  name: z.string().min(1),
  description: z.string().min(1),
  category: z.string().optional(),
  affiliateUrl: z.string().url(),
});

export const validateProduct = (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  const result = productSchema.safeParse(req.body);

  if (!result.success) {
    return res.status(400).json(result.error.format());
  }

  next();
};
