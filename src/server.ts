import express, { Application } from 'express';
import cors from 'cors';
// import { PrismaClient } from '@prisma/client';
import productRoutes from './routes/productRoutes';

const app: Application = express();
// const prisma = new PrismaClient();

app.use(cors());
app.use(express.json());
app.use('/api/products', productRoutes);

// Only listen if we're _not_ in the Jest test environment
if (process.env.NODE_ENV !== 'test') {
  const PORT = process.env.PORT || 5050;
  app.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
  });
}

// Export the app for import into your test files
export default app;
