import express from 'express';
import {
  getProducts,
  createProduct,
  updateProduct,
  deleteProduct,
} from '../controllers/productControllers';
import { validateProduct } from '../middlewares/validateProducts';

const router = express.Router();

router.get('/', getProducts);
// router.post('/', createProduct)
router.post('/', validateProduct, createProduct);
router.put('/:id', updateProduct);
router.delete('/:id', deleteProduct);

export default router;
