// eslint.config.js
import pkg from '@eslint/js';                     // default import for @eslint/js
const { configs: { recommended: jsRecommended } } = pkg;

import tsPlugin from '@typescript-eslint/eslint-plugin';
import tsParser from '@typescript-eslint/parser';
import prettierPlugin from 'eslint-plugin-prettier';
import prettierConfig from 'eslint-config-prettier';

export default [
  // 1) Base JS recommended rules
  jsRecommended,

  // 2) Ignore build output and deps
  {
    ignores: ['dist/**', 'node_modules/**'],
  },

  // 3) Only apply the rest to your TS files
  {
    files: ['**/*.ts', '**/*.tsx'],
    languageOptions: {
      parser: tsParser,
      parserOptions: {
        ecmaVersion: 'latest',
        sourceType: 'module',
      },
      // <-- flat config no longer supports `env`; instead list globals here:
      globals: {
        process: 'readonly',
        console: 'readonly',
      },
    },
    plugins: {
      '@typescript-eslint': tsPlugin,
      prettier: prettierPlugin,
    },
    rules: {
      // a) bring in all of the TS plugin’s recommended rules
      ...tsPlugin.configs.recommended.rules,

      // b) turn off anything Prettier will handle
      ...prettierConfig.rules,

      // c) treat Prettier formatting issues as errors
      'prettier/prettier': 'error',

      // d) your custom tweaks:
      '@typescript-eslint/explicit-function-return-type': 'off',
      '@typescript-eslint/no-explicit-any': 'warn',

      // e) allow console.log + warn + error (but warn on use)
      'no-console': ['warn', { allow: ['log', 'warn', 'error'] }],
    },
  },
];
