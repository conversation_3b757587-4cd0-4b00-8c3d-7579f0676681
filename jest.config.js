// jest.config.js
export default {
  preset: 'ts-jest',
  testEnvironment: 'node',

  // → collect coverage and output it under /coverage
  collectCoverage: true,
  coverageDirectory: 'coverage',
  // → only look at your source files
  collectCoverageFrom: [
    'src/**/*.ts', 
    '!src/server.ts'    // optionally exclude your entry point
  ],
  // → enforce minimum coverage thresholds
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
  moduleFileExtensions: ['ts','js'],
  testMatch: ['**/?(*.)+(spec|test).[tj]s'],
};
