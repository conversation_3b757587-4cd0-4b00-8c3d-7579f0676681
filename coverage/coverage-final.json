{"/Users/<USER>/personalProjects/software-app/new-back-end/src/controllers/productControllers.ts": {"path": "/Users/<USER>/personalProjects/software-app/new-back-end/src/controllers/productControllers.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 46}}, "1": {"start": {"line": 3, "column": 15}, "end": {"line": 3, "column": 33}}, "2": {"start": {"line": 5, "column": 27}, "end": {"line": 20, "column": 1}}, "3": {"start": {"line": 9, "column": 2}, "end": {"line": 19, "column": 3}}, "4": {"start": {"line": 10, "column": 21}, "end": {"line": 10, "column": 52}}, "5": {"start": {"line": 11, "column": 4}, "end": {"line": 11, "column": 23}}, "6": {"start": {"line": 14, "column": 4}, "end": {"line": 16, "column": 5}}, "7": {"start": {"line": 15, "column": 6}, "end": {"line": 15, "column": 53}}, "8": {"start": {"line": 18, "column": 4}, "end": {"line": 18, "column": 69}}, "9": {"start": {"line": 5, "column": 13}, "end": {"line": 5, "column": 27}}, "10": {"start": {"line": 22, "column": 29}, "end": {"line": 40, "column": 1}}, "11": {"start": {"line": 26, "column": 2}, "end": {"line": 39, "column": 3}}, "12": {"start": {"line": 27, "column": 58}, "end": {"line": 27, "column": 66}}, "13": {"start": {"line": 28, "column": 23}, "end": {"line": 30, "column": 6}}, "14": {"start": {"line": 31, "column": 4}, "end": {"line": 31, "column": 25}}, "15": {"start": {"line": 34, "column": 4}, "end": {"line": 36, "column": 5}}, "16": {"start": {"line": 35, "column": 6}, "end": {"line": 35, "column": 53}}, "17": {"start": {"line": 38, "column": 4}, "end": {"line": 38, "column": 69}}, "18": {"start": {"line": 22, "column": 13}, "end": {"line": 22, "column": 29}}, "19": {"start": {"line": 42, "column": 29}, "end": {"line": 62, "column": 1}}, "20": {"start": {"line": 46, "column": 2}, "end": {"line": 61, "column": 3}}, "21": {"start": {"line": 47, "column": 19}, "end": {"line": 47, "column": 29}}, "22": {"start": {"line": 48, "column": 17}, "end": {"line": 48, "column": 25}}, "23": {"start": {"line": 49, "column": 27}, "end": {"line": 52, "column": 6}}, "24": {"start": {"line": 53, "column": 4}, "end": {"line": 53, "column": 29}}, "25": {"start": {"line": 56, "column": 4}, "end": {"line": 58, "column": 5}}, "26": {"start": {"line": 57, "column": 6}, "end": {"line": 57, "column": 53}}, "27": {"start": {"line": 60, "column": 4}, "end": {"line": 60, "column": 69}}, "28": {"start": {"line": 42, "column": 13}, "end": {"line": 42, "column": 29}}, "29": {"start": {"line": 64, "column": 29}, "end": {"line": 80, "column": 1}}, "30": {"start": {"line": 68, "column": 2}, "end": {"line": 79, "column": 3}}, "31": {"start": {"line": 69, "column": 19}, "end": {"line": 69, "column": 29}}, "32": {"start": {"line": 70, "column": 4}, "end": {"line": 70, "column": 65}}, "33": {"start": {"line": 71, "column": 4}, "end": {"line": 71, "column": 45}}, "34": {"start": {"line": 74, "column": 4}, "end": {"line": 76, "column": 5}}, "35": {"start": {"line": 75, "column": 6}, "end": {"line": 75, "column": 53}}, "36": {"start": {"line": 78, "column": 4}, "end": {"line": 78, "column": 69}}, "37": {"start": {"line": 64, "column": 13}, "end": {"line": 64, "column": 29}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 27}, "end": {"line": 5, "column": 32}}, "loc": {"start": {"line": 8, "column": 19}, "end": {"line": 20, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 22, "column": 29}, "end": {"line": 22, "column": 34}}, "loc": {"start": {"line": 25, "column": 19}, "end": {"line": 40, "column": 1}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 42, "column": 29}, "end": {"line": 42, "column": 34}}, "loc": {"start": {"line": 45, "column": 19}, "end": {"line": 62, "column": 1}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 64, "column": 29}, "end": {"line": 64, "column": 34}}, "loc": {"start": {"line": 67, "column": 19}, "end": {"line": 80, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 14, "column": 4}, "end": {"line": 16, "column": 5}}, "type": "if", "locations": [{"start": {"line": 14, "column": 4}, "end": {"line": 16, "column": 5}}, {"start": {}, "end": {}}]}, "1": {"loc": {"start": {"line": 34, "column": 4}, "end": {"line": 36, "column": 5}}, "type": "if", "locations": [{"start": {"line": 34, "column": 4}, "end": {"line": 36, "column": 5}}, {"start": {}, "end": {}}]}, "2": {"loc": {"start": {"line": 56, "column": 4}, "end": {"line": 58, "column": 5}}, "type": "if", "locations": [{"start": {"line": 56, "column": 4}, "end": {"line": 58, "column": 5}}, {"start": {}, "end": {}}]}, "3": {"loc": {"start": {"line": 74, "column": 4}, "end": {"line": 76, "column": 5}}, "type": "if", "locations": [{"start": {"line": 74, "column": 4}, "end": {"line": 76, "column": 5}}, {"start": {}, "end": {}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 1, "10": 1, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 1, "19": 1, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 1, "29": 1, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 1}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0]}}, "/Users/<USER>/personalProjects/software-app/new-back-end/src/middlewares/validateProducts.ts": {"path": "/Users/<USER>/personalProjects/software-app/new-back-end/src/middlewares/validateProducts.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 24}}, "1": {"start": {"line": 4, "column": 22}, "end": {"line": 9, "column": 2}}, "2": {"start": {"line": 11, "column": 31}, "end": {"line": 23, "column": 1}}, "3": {"start": {"line": 16, "column": 17}, "end": {"line": 16, "column": 50}}, "4": {"start": {"line": 18, "column": 2}, "end": {"line": 20, "column": 3}}, "5": {"start": {"line": 19, "column": 4}, "end": {"line": 19, "column": 55}}, "6": {"start": {"line": 22, "column": 2}, "end": {"line": 22, "column": 9}}, "7": {"start": {"line": 11, "column": 13}, "end": {"line": 11, "column": 31}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 11, "column": 31}, "end": {"line": 11, "column": null}}, "loc": {"start": {"line": 15, "column": 4}, "end": {"line": 23, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 18, "column": 2}, "end": {"line": 20, "column": 3}}, "type": "if", "locations": [{"start": {"line": 18, "column": 2}, "end": {"line": 20, "column": 3}}, {"start": {}, "end": {}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 0, "4": 0, "5": 0, "6": 0, "7": 1}, "f": {"0": 0}, "b": {"0": [0, 0]}}, "/Users/<USER>/personalProjects/software-app/new-back-end/src/routes/productRoutes.ts": {"path": "/Users/<USER>/personalProjects/software-app/new-back-end/src/routes/productRoutes.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 30}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": null}}, "2": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 66}}, "3": {"start": {"line": 10, "column": 15}, "end": {"line": 10, "column": 31}}, "4": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 29}}, "5": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 49}}, "6": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 34}}, "7": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 37}}, "8": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 22}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1}, "f": {}, "b": {}}}