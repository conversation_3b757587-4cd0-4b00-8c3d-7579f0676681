<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1752552720284" clover="3.2.0">
  <project timestamp="1752552720284" name="All files">
    <metrics statements="50" coveredstatements="18" conditionals="10" coveredconditionals="0" methods="5" coveredmethods="0" elements="65" coveredelements="18" complexity="0" loc="50" ncloc="50" packages="3" files="3" classes="3"/>
    <package name="controllers">
      <metrics statements="34" coveredstatements="6" conditionals="8" coveredconditionals="0" methods="4" coveredmethods="0"/>
      <file name="productControllers.ts" path="/Users/<USER>/personalProjects/software-app/new-back-end/src/controllers/productControllers.ts">
        <metrics statements="34" coveredstatements="6" conditionals="8" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="14" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="15" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="22" count="1" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="35" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="56" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="57" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="74" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="75" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
      </file>
    </package>
    <package name="middlewares">
      <metrics statements="7" coveredstatements="3" conditionals="2" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="validateProducts.ts" path="/Users/<USER>/personalProjects/software-app/new-back-end/src/middlewares/validateProducts.ts">
        <metrics statements="7" coveredstatements="3" conditionals="2" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="18" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="19" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
      </file>
    </package>
    <package name="routes">
      <metrics statements="9" coveredstatements="9" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="productRoutes.ts" path="/Users/<USER>/personalProjects/software-app/new-back-end/src/routes/productRoutes.ts">
        <metrics statements="9" coveredstatements="9" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
