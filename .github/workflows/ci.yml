name: CI

on:
  push:
    branches:
      - main
      - develop
  pull_request:
    branches:
      - main
      - develop

jobs:
  build-and-test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: testdb
        ports:
          - 5432:5432
        options: >-
          --health-cmd "pg_isready -U postgres"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    env:
      # Point Prisma at the ephemeral Postgres service
      DATABASE_URL: postgres://postgres:postgres@localhost:5432/testdb

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Use Node.js 20
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'yarn'

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Generate Prisma client
        run: yarn prisma:generate

      - name: Apply database migrations
        run: yarn prisma:migrate deploy

      - name: Run lint
        run: yarn lint

      - name: Build TypeScript
        run: yarn build

      - name: Run tests
        run: yarn test
