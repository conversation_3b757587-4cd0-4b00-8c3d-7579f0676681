{"name": "new-back-end", "version": "1.0.0", "main": "index.js", "author": "<PERSON>", "license": "MIT", "type": "module", "scripts": {"dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "tsc", "start": "node dist/server.js", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate deploy", "prisma:studio": "prisma studio", "lint": "eslint \"src/**/*.ts\"", "format": "prettier --write \"src/**/*.ts\"", "test": "jest --passWithNoTests", "test:watch": "jest --watch"}, "dependencies": {"@prisma/client": "^6.11.1", "cors": "^2.8.5", "express": "^5.1.0", "zod": "^4.0.5"}, "devDependencies": {"@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^24.0.13", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "jest": "^30.0.4", "prettier": "^3.6.2", "prisma": "^6.11.1", "supertest": "^7.1.3", "ts-jest": "^29.4.0", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}